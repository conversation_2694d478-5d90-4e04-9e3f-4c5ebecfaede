#!/usr/bin/env python3
"""
Binance WebSocket Integration for Real-time Price Monitoring
Provides real-time price feeds for unlock event detection and trading signals.
"""

import json
import logging
import threading
import time
from typing import Dict, List, Callable, Optional
from decimal import Decimal
import websocket
from datetime import datetime, timedelta

class BinanceWebSocketManager:
    """
    Manages Binance WebSocket connections for real-time price monitoring.
    Detects unusual price movements that could indicate unlock events.
    """
    
    def __init__(self):
        self.ws = None
        self.is_connected = False
        self.price_data = {}
        self.price_history = {}
        self.callbacks = []
        self.monitored_symbols = set()
        self.unlock_detection_enabled = True
        
        # Price movement thresholds for unlock detection
        self.PRICE_DROP_THRESHOLD_1H = -10.0  # 10% drop in 1 hour
        self.PRICE_DROP_THRESHOLD_24H = -15.0  # 15% drop in 24 hours
        self.VOLUME_SPIKE_THRESHOLD = 3.0  # 3x normal volume
        
    def add_symbol(self, symbol: str):
        """Add a symbol to monitor"""
        symbol = symbol.upper()
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
        self.monitored_symbols.add(symbol)
        logging.info(f"Added {symbol} to Binance monitoring")
        
    def remove_symbol(self, symbol: str):
        """Remove a symbol from monitoring"""
        symbol = symbol.upper()
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
        self.monitored_symbols.discard(symbol)
        logging.info(f"Removed {symbol} from Binance monitoring")
        
    def add_callback(self, callback: Callable):
        """Add a callback function for price updates"""
        self.callbacks.append(callback)
        
    def start_monitoring(self, symbols: List[str] = None):
        """Start WebSocket monitoring for specified symbols"""
        if symbols:
            for symbol in symbols:
                self.add_symbol(symbol)
        
        if not self.monitored_symbols:
            # Default symbols for unlock monitoring
            default_symbols = [
                'UNIUSDT', 'AAVEUSDT', 'COMPUSDT', 'DYDXUSDT', 'LINKUSDT',
                'MKRUSDT', 'YFIUSDT', 'CRVUSDT', 'CVXUSDT', 'LRCUSDT'
            ]
            for symbol in default_symbols:
                self.add_symbol(symbol)
        
        self._connect()
        
    def _connect(self):
        """Establish WebSocket connection with improved reliability"""
        try:
            # Limit to 3 symbols to avoid connection issues
            symbols_to_monitor = list(self.monitored_symbols)[:3]

            if not symbols_to_monitor:
                logging.warning("No symbols to monitor")
                return

            # Use individual ticker streams (more reliable than combined)
            streams = []
            for symbol in symbols_to_monitor:
                streams.append(f"{symbol.lower()}@ticker")

            # Use the multi-stream endpoint (more reliable)
            ws_url = "wss://stream.binance.com:9443/stream"

            logging.info(f"Connecting to Binance WebSocket: {len(symbols_to_monitor)} symbols")
            logging.info(f"Monitoring: {', '.join(symbols_to_monitor)}")

            self.ws = websocket.WebSocketApp(
                ws_url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open
            )

            # Start WebSocket in a separate thread with keep-alive
            ws_thread = threading.Thread(target=self._run_with_keepalive)
            ws_thread.daemon = True
            ws_thread.start()

        except Exception as e:
            logging.error(f"Failed to connect to Binance WebSocket: {e}")

    def _run_with_keepalive(self):
        """Run WebSocket with keep-alive mechanism"""
        try:
            # Run forever with ping interval for keep-alive
            self.ws.run_forever(
                ping_interval=20,  # Send ping every 20 seconds
                ping_timeout=10,   # Wait 10 seconds for pong
                ping_payload="ping"
            )
        except Exception as e:
            logging.error(f"WebSocket run_forever failed: {e}")

    def _subscribe_to_streams(self):
        """Subscribe to specific streams after connection"""
        try:
            symbols_to_monitor = list(self.monitored_symbols)[:3]

            # Subscribe to ticker streams
            for symbol in symbols_to_monitor:
                subscribe_msg = {
                    "method": "SUBSCRIBE",
                    "params": [f"{symbol.lower()}@ticker"],
                    "id": hash(symbol) % 1000
                }

                if self.ws and self.is_connected:
                    self.ws.send(json.dumps(subscribe_msg))
                    logging.info(f"Subscribed to {symbol} ticker stream")
                    time.sleep(0.1)  # Small delay between subscriptions

        except Exception as e:
            logging.error(f"Failed to subscribe to streams: {e}")
            
    def _on_open(self, ws):
        """WebSocket connection opened"""
        self.is_connected = True
        logging.info("✅ Binance WebSocket connected successfully")

        # Subscribe to streams after connection is established
        time.sleep(1)  # Wait a moment for connection to stabilize
        self._subscribe_to_streams()
        
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed"""
        self.is_connected = False

        if close_status_code:
            logging.warning(f"❌ Binance WebSocket closed: {close_status_code} - {close_msg}")
        else:
            logging.info("ℹ️ Binance WebSocket connection closed normally")

        # Only attempt to reconnect if we have symbols to monitor and it wasn't a normal close
        if self.monitored_symbols and close_status_code != 1000:
            logging.info("🔄 Attempting to reconnect in 5 seconds...")
            time.sleep(5)
            self._connect()
            
    def _on_error(self, ws, error):
        """WebSocket error occurred"""
        logging.error(f"Binance WebSocket error: {error}")
        
    def _on_message(self, ws, message):
        """Process incoming WebSocket message"""
        try:
            data = json.loads(message)
            
            if 'stream' in data:
                stream = data['stream']
                stream_data = data['data']
                
                if '@ticker' in stream:
                    self._process_ticker_data(stream_data)
                elif '@kline' in stream:
                    self._process_kline_data(stream_data)
                    
        except Exception as e:
            logging.error(f"Error processing Binance message: {e}")
            
    def _process_ticker_data(self, data):
        """Process 24hr ticker statistics"""
        symbol = data['s']
        price = float(data['c'])  # Current price
        price_change_pct = float(data['P'])  # 24hr price change %
        volume = float(data['v'])  # 24hr volume
        
        # Store current data
        self.price_data[symbol] = {
            'price': price,
            'price_change_24h': price_change_pct,
            'volume_24h': volume,
            'timestamp': datetime.now()
        }
        
        # Check for unlock indicators
        if self.unlock_detection_enabled:
            self._check_unlock_indicators(symbol, data)
            
        # Notify callbacks
        for callback in self.callbacks:
            try:
                callback('ticker', symbol, data)
            except Exception as e:
                logging.error(f"Callback error: {e}")
                
    def _process_kline_data(self, data):
        """Process kline/candlestick data"""
        symbol = data['s']
        kline = data['k']
        
        if kline['x']:  # Kline is closed
            open_price = float(kline['o'])
            close_price = float(kline['c'])
            high_price = float(kline['h'])
            low_price = float(kline['l'])
            volume = float(kline['v'])
            
            # Calculate hourly change
            hourly_change = ((close_price - open_price) / open_price) * 100
            
            # Store in price history
            if symbol not in self.price_history:
                self.price_history[symbol] = []
                
            self.price_history[symbol].append({
                'timestamp': datetime.now(),
                'open': open_price,
                'close': close_price,
                'high': high_price,
                'low': low_price,
                'volume': volume,
                'change_pct': hourly_change
            })
            
            # Keep only last 24 hours of data
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.price_history[symbol] = [
                h for h in self.price_history[symbol] 
                if h['timestamp'] > cutoff_time
            ]
            
    def _check_unlock_indicators(self, symbol: str, ticker_data):
        """Check for potential unlock event indicators"""
        try:
            price_change_24h = float(ticker_data['P'])
            volume_24h = float(ticker_data['v'])
            
            # Get historical volume for comparison
            avg_volume = self._get_average_volume(symbol)
            volume_ratio = volume_24h / avg_volume if avg_volume > 0 else 1
            
            # Detect unlock indicators
            indicators = []
            
            # Significant price drop
            if price_change_24h <= self.PRICE_DROP_THRESHOLD_24H:
                indicators.append(f"24h price drop: {price_change_24h:.2f}%")
                
            # Volume spike with price drop
            if price_change_24h < -5 and volume_ratio >= self.VOLUME_SPIKE_THRESHOLD:
                indicators.append(f"Volume spike: {volume_ratio:.1f}x normal")
                
            # Check hourly movements
            if symbol in self.price_history and self.price_history[symbol]:
                recent_changes = [h['change_pct'] for h in self.price_history[symbol][-3:]]
                if any(change <= self.PRICE_DROP_THRESHOLD_1H for change in recent_changes):
                    indicators.append("Rapid hourly decline detected")
                    
            if indicators:
                self._alert_unlock_indicator(symbol, indicators, ticker_data)
                
        except Exception as e:
            logging.error(f"Error checking unlock indicators for {symbol}: {e}")
            
    def _get_average_volume(self, symbol: str) -> float:
        """Calculate average volume from price history"""
        if symbol not in self.price_history or not self.price_history[symbol]:
            return 0
            
        volumes = [h['volume'] for h in self.price_history[symbol]]
        return sum(volumes) / len(volumes) if volumes else 0
        
    def _alert_unlock_indicator(self, symbol: str, indicators: List[str], ticker_data):
        """Alert about potential unlock event"""
        price = float(ticker_data['c'])
        price_change = float(ticker_data['P'])
        
        alert_message = f"🚨 UNLOCK INDICATOR: {symbol}\n"
        alert_message += f"Price: ${price:.4f} ({price_change:+.2f}%)\n"
        alert_message += f"Indicators: {', '.join(indicators)}\n"
        alert_message += f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        logging.warning(alert_message)
        
        # Notify callbacks about unlock indicator
        for callback in self.callbacks:
            try:
                callback('unlock_indicator', symbol, {
                    'symbol': symbol,
                    'price': price,
                    'price_change_24h': price_change,
                    'indicators': indicators,
                    'timestamp': datetime.now(),
                    'confidence': 'medium'
                })
            except Exception as e:
                logging.error(f"Unlock indicator callback error: {e}")
                
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        symbol = symbol.upper()
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
            
        if symbol in self.price_data:
            return self.price_data[symbol]['price']
        return None
        
    def get_price_change_24h(self, symbol: str) -> Optional[float]:
        """Get 24h price change percentage for a symbol"""
        symbol = symbol.upper()
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
            
        if symbol in self.price_data:
            return self.price_data[symbol]['price_change_24h']
        return None
        
    def stop_monitoring(self):
        """Stop WebSocket monitoring"""
        self.unlock_detection_enabled = False
        if self.ws:
            self.ws.close()
        self.is_connected = False
        logging.info("Binance WebSocket monitoring stopped")

# Global instance for easy access
binance_ws_manager = BinanceWebSocketManager()

def start_binance_monitoring(symbols: List[str] = None, callback: Callable = None):
    """Convenience function to start Binance monitoring"""
    if callback:
        binance_ws_manager.add_callback(callback)
    binance_ws_manager.start_monitoring(symbols)
    return binance_ws_manager

if __name__ == "__main__":
    # Test the WebSocket connection
    logging.basicConfig(level=logging.INFO)
    
    def test_callback(event_type, symbol, data):
        if event_type == 'unlock_indicator':
            print(f"🚨 UNLOCK ALERT: {symbol} - {data['indicators']}")
        elif event_type == 'ticker':
            price = data['c']
            change = data['P']
            print(f"📊 {symbol}: ${price} ({change:+.2f}%)")
    
    # Start monitoring with test callback
    manager = start_binance_monitoring(['UNI', 'AAVE', 'COMP'], test_callback)
    
    try:
        # Keep running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping Binance monitoring...")
        manager.stop_monitoring()
