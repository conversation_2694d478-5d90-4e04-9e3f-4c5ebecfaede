#!/usr/bin/env python3
"""
Complete System Test with Real API Keys
Tests all components with actual Infura and CoinGecko keys
"""

import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_api_keys():
    """Test all API key configurations"""
    print("🔑 API Keys Configuration Test")
    print("=" * 50)
    
    api_keys = {
        'INFURA_API_KEY': {
            'value': os.environ.get('INFURA_API_KEY'),
            'description': 'Ethereum blockchain access',
            'critical': True
        },
        'COINGECKO_API_KEY': {
            'value': os.environ.get('COINGECKO_API_KEY'),
            'description': 'Market data and price analysis',
            'critical': False
        },
        'DEXSCREENER_API_KEY': {
            'value': os.environ.get('DEXSCREENER_API_KEY'),
            'description': 'DEX analytics',
            'critical': False
        },
        'DEXTOOLS_API_KEY': {
            'value': os.environ.get('DEXTOOLS_API_KEY'),
            'description': 'Token scoring and trending',
            'critical': False
        },
        'DEFILLAMA_PRO_API_KEY': {
            'value': os.environ.get('DEFILLAMA_PRO_API_KEY'),
            'description': 'DeFiLlama PRO features',
            'critical': False
        }
    }
    
    active_keys = 0
    critical_keys = 0
    
    for key_name, info in api_keys.items():
        value = info['value']
        is_set = value and len(value) > 10 and not value.startswith('your_')
        
        if is_set:
            status = f"✅ ACTIVE ({value[:8]}...)"
            active_keys += 1
            if info['critical']:
                critical_keys += 1
        else:
            status = "⭕ Not set"
        
        critical_mark = " (CRITICAL)" if info['critical'] else ""
        print(f"  {key_name}: {status}{critical_mark}")
        print(f"    {info['description']}")
    
    print(f"\n📊 Summary: {active_keys} active keys, {critical_keys} critical keys active")
    return active_keys, critical_keys

def test_blockchain_connectivity():
    """Test blockchain connectivity with Infura"""
    print("\n🔗 Blockchain Connectivity Test")
    print("=" * 50)
    
    try:
        from web3 import Web3
        
        infura_key = os.environ.get('INFURA_API_KEY')
        if not infura_key or len(infura_key) < 10:
            print("❌ Infura API key not set")
            return False
        
        # Test Ethereum mainnet connection
        infura_url = f"https://mainnet.infura.io/v3/{infura_key}"
        w3 = Web3(Web3.HTTPProvider(infura_url))
        
        print(f"🔗 Connecting to: mainnet.infura.io")
        
        # Test connection
        if w3.is_connected():
            print("✅ Connected to Ethereum mainnet")
            
            # Get latest block
            latest_block = w3.eth.block_number
            print(f"📦 Latest block: {latest_block:,}")
            
            # Test gas price
            gas_price = w3.eth.gas_price
            gas_price_gwei = w3.from_wei(gas_price, 'gwei')
            print(f"⛽ Current gas price: {gas_price_gwei:.2f} Gwei")
            
            return True
        else:
            print("❌ Failed to connect to Ethereum")
            return False
            
    except Exception as e:
        print(f"❌ Blockchain test failed: {e}")
        return False

def test_data_sources():
    """Test data source integrations"""
    print("\n📊 Data Sources Integration Test")
    print("=" * 50)
    
    try:
        # Add services to path
        sys.path.append('services/the-oracle')
        from data_sources import fetch_token_unlocks_data
        
        print("🚀 Fetching unlock data from all sources...")
        events = fetch_token_unlocks_data()
        
        print(f"✅ Total events detected: {len(events)}")
        
        # Group by source
        sources = {}
        for event in events:
            source = event.get('source', 'Unknown')
            if source not in sources:
                sources[source] = 0
            sources[source] += 1
        
        print("\n📈 Events by source:")
        for source, count in sources.items():
            print(f"  - {source}: {count} events")
        
        # Show sample events with real data
        if events:
            print("\n🎯 Sample unlock events:")
            for i, event in enumerate(events[:3]):
                symbol = event.get('token_symbol', 'UNKNOWN')
                source = event.get('source', 'Unknown')
                confidence = event.get('confidence', 'unknown')
                
                # Show additional details if available
                details = []
                if 'price_change_24h' in event:
                    details.append(f"{event['price_change_24h']:+.2f}% price change")
                if 'tvl_change_1d' in event:
                    details.append(f"{event['tvl_change_1d']:+.2f}% TVL change")
                
                detail_str = f" ({', '.join(details)})" if details else ""
                print(f"  {i+1}. {symbol} from {source}{detail_str}")
        
        return len(events) > 0
        
    except Exception as e:
        print(f"❌ Data sources test failed: {e}")
        return False

def test_aave_integration():
    """Test Aave integration with real blockchain data"""
    print("\n🏦 Aave Integration Test")
    print("=" * 50)
    
    try:
        sys.path.append('services/aave-integration')
        sys.path.append('.')
        from services.aave_integration.aave_integration import check_token_borrowability
        
        # Test with real tokens
        test_tokens = [
            ('UNI', '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984'),
            ('AAVE', '0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9'),
            ('COMP', '0xc00e94cb662c3520282e6f5717214004a7f26888')
        ]
        
        borrowable_count = 0
        
        for symbol, address in test_tokens:
            print(f"🔍 Testing {symbol}...")
            
            try:
                is_borrowable, info = check_token_borrowability(address)
                
                if is_borrowable:
                    print(f"  ✅ {symbol} is borrowable")
                    borrowable_count += 1
                else:
                    print(f"  ⭕ {symbol} not borrowable")
                
                # Show protocol info
                if info.get('is_major_token'):
                    print(f"  📊 Major token: Yes")
                
            except Exception as e:
                print(f"  ❌ Error testing {symbol}: {e}")
        
        print(f"\n📊 Result: {borrowable_count}/{len(test_tokens)} tokens borrowable")
        return borrowable_count > 0
        
    except Exception as e:
        print(f"❌ Aave integration test failed: {e}")
        return False

def test_pressure_score():
    """Test pressure score calculation"""
    print("\n📈 Pressure Score Calculation Test")
    print("=" * 50)
    
    try:
        sys.path.append('services/pressure-score')
        sys.path.append('.')
        from services.pressure_score.pressure_score import calculate_pressure_score
        
        # Test with realistic unlock scenario
        test_event = {
            'token_symbol': 'UNI',
            'unlock_amount': 100000000,  # 100M tokens
            'circulating_supply': 750000000,  # 750M tokens
            'total_supply': 1000000000,  # 1B tokens
            'unlock_date': '2024-12-01T00:00:00Z'
        }
        
        print(f"🧮 Testing pressure score for {test_event['token_symbol']}...")
        print(f"  Unlock amount: {test_event['unlock_amount']:,}")
        print(f"  Circulating supply: {test_event['circulating_supply']:,}")
        
        score = calculate_pressure_score(test_event)
        
        print(f"  🎯 Pressure Score: {score:.4f}")
        
        if score > 0.75:
            print(f"  ✅ High pressure - Trade candidate")
        elif score > 0.5:
            print(f"  ⚠️ Medium pressure - Monitor closely")
        else:
            print(f"  ℹ️ Low pressure - Low priority")
        
        return True
        
    except Exception as e:
        print(f"❌ Pressure score test failed: {e}")
        return False

def main():
    """Run complete system test"""
    print("🧪 Project Chimera - Complete System Test")
    print("Testing with Real API Keys: Infura + CoinGecko")
    print("=" * 60)
    
    tests = [
        ("API Keys Configuration", test_api_keys),
        ("Blockchain Connectivity", test_blockchain_connectivity),
        ("Data Sources Integration", test_data_sources),
        ("Aave Integration", test_aave_integration),
        ("Pressure Score Calculation", test_pressure_score),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if test_name == "API Keys Configuration":
                active_keys, critical_keys = test_func()
                result = critical_keys > 0  # Pass if we have critical keys
                results.append((test_name, result, f"{active_keys} active"))
            else:
                result = test_func()
                results.append((test_name, result, ""))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False, "crashed"))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Complete System Test Results:")
    
    passed = 0
    for test_name, result, note in results:
        status = "✅ PASS" if result else "❌ FAIL"
        note_str = f" ({note})" if note else ""
        print(f"  {status} - {test_name}{note_str}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 ALL TESTS PASSED! System is fully operational with real APIs.")
        print("✅ Ready for production trading with:")
        print("   - Real blockchain connectivity (Infura)")
        print("   - Live market data (CoinGecko)")
        print("   - Working unlock detection")
        print("   - Functional Aave integration")
    elif passed >= len(results) * 0.8:
        print("✅ MOSTLY WORKING! System is functional with minor issues.")
        print("🔧 Address any failed tests for optimal performance.")
    else:
        print("⚠️ SOME ISSUES DETECTED. Check failed tests.")
        print("💡 System may still work but with reduced functionality.")
    
    print(f"\n💰 Current cost: $0/month (using free tiers)")
    print(f"🚀 Upgrade options available for enhanced features")

if __name__ == "__main__":
    main()
