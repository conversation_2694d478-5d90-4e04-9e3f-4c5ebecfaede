#!/usr/bin/env python3
"""
Telegram Notification System for Project Chimera
Sends real-time alerts for unlock events, trading signals, and system status
"""

import os
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional
from dotenv import load_dotenv
from telegram import Bo<PERSON>
from telegram.error import TelegramError

# Load environment variables
load_dotenv()

class ChimeraTelegramNotifier:
    """
    Telegram notification system for Project Chimera.
    Handles unlock alerts, trading notifications, and system monitoring.
    """
    
    def __init__(self):
        # Load Telegram credentials
        self.bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
        self.chat_id = os.environ.get('TELEGRAM_CHAT_ID')
        self.admin_chat_id = os.environ.get('TELEGRAM_ADMIN_CHAT_ID')
        
        # Initialize bot
        self.bot = None
        self.is_configured = False
        
        if self.bot_token and self.chat_id:
            self.bot = Bot(token=self.bot_token)
            self.is_configured = True
            logging.info("✅ Telegram notifier configured")
        else:
            logging.warning("⚠️ Telegram credentials not set")
    
    async def send_message(self, message: str, chat_id: Optional[str] = None, parse_mode: str = 'HTML') -> bool:
        """Send a message to Telegram"""
        if not self.is_configured:
            logging.warning("Telegram not configured - message not sent")
            return False
        
        try:
            target_chat_id = chat_id or self.chat_id
            await self.bot.send_message(
                chat_id=target_chat_id,
                text=message,
                parse_mode=parse_mode
            )
            return True
            
        except TelegramError as e:
            logging.error(f"Telegram error: {e}")
            return False
        except Exception as e:
            logging.error(f"Failed to send Telegram message: {e}")
            return False
    
    async def send_unlock_alert(self, unlock_event: Dict) -> bool:
        """Send unlock event alert"""
        try:
            symbol = unlock_event.get('token_symbol', 'UNKNOWN')
            source = unlock_event.get('source', 'Unknown')
            confidence = unlock_event.get('confidence', 'unknown')
            
            # Create alert message
            message = f"🚨 <b>UNLOCK ALERT</b> 🚨\n\n"
            message += f"🪙 <b>Token:</b> {symbol}\n"
            message += f"📊 <b>Source:</b> {source}\n"
            message += f"🎯 <b>Confidence:</b> {confidence.title()}\n"
            
            # Add specific details based on source
            if 'price_change_24h' in unlock_event:
                change = unlock_event['price_change_24h']
                message += f"📉 <b>Price Change:</b> {change:+.2f}%\n"
            
            if 'tvl_change_1d' in unlock_event:
                tvl_change = unlock_event['tvl_change_1d']
                message += f"💰 <b>TVL Change:</b> {tvl_change:+.2f}%\n"
            
            if 'unlock_amount' in unlock_event:
                amount = unlock_event['unlock_amount']
                message += f"🔓 <b>Unlock Amount:</b> {amount:,.0f}\n"
            
            if 'unlock_date' in unlock_event:
                date = unlock_event['unlock_date']
                message += f"📅 <b>Unlock Date:</b> {date}\n"
            
            message += f"\n⏰ <b>Detected:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            message += f"\n🤖 <i>Project Chimera Alert System</i>"
            
            return await self.send_message(message)
            
        except Exception as e:
            logging.error(f"Failed to send unlock alert: {e}")
            return False
    
    async def send_trading_signal(self, signal_type: str, token: str, details: Dict) -> bool:
        """Send trading signal notification"""
        try:
            if signal_type == 'ENTRY':
                emoji = "🟢"
                action = "ENTERING SHORT POSITION"
            elif signal_type == 'EXIT':
                emoji = "🔴"
                action = "EXITING POSITION"
            elif signal_type == 'STOP_LOSS':
                emoji = "⛔"
                action = "STOP LOSS TRIGGERED"
            elif signal_type == 'TAKE_PROFIT':
                emoji = "💰"
                action = "TAKE PROFIT TRIGGERED"
            else:
                emoji = "📊"
                action = signal_type
            
            message = f"{emoji} <b>{action}</b>\n\n"
            message += f"🪙 <b>Token:</b> {token}\n"
            
            if 'pressure_score' in details:
                score = details['pressure_score']
                message += f"⚡ <b>Pressure Score:</b> {score:.4f}\n"
            
            if 'entry_price' in details:
                price = details['entry_price']
                message += f"💵 <b>Entry Price:</b> ${price:.4f}\n"
            
            if 'exit_price' in details:
                price = details['exit_price']
                message += f"💵 <b>Exit Price:</b> ${price:.4f}\n"
            
            if 'pnl' in details:
                pnl = details['pnl']
                pnl_emoji = "💚" if pnl > 0 else "❤️"
                message += f"{pnl_emoji} <b>P&L:</b> ${pnl:+.2f}\n"
            
            if 'amount' in details:
                amount = details['amount']
                message += f"📊 <b>Amount:</b> {amount:,.2f}\n"
            
            message += f"\n⏰ <b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            message += f"\n🤖 <i>Project Chimera Trading Bot</i>"
            
            return await self.send_message(message)
            
        except Exception as e:
            logging.error(f"Failed to send trading signal: {e}")
            return False
    
    async def send_system_status(self, status: Dict) -> bool:
        """Send system status update"""
        try:
            message = f"📊 <b>SYSTEM STATUS</b>\n\n"
            
            # API Status
            message += f"🔑 <b>API Status:</b>\n"
            if status.get('infura_connected'):
                message += f"  ✅ Infura: Connected\n"
            else:
                message += f"  ❌ Infura: Disconnected\n"
            
            if status.get('coingecko_active'):
                message += f"  ✅ CoinGecko: Active\n"
            else:
                message += f"  ⭕ CoinGecko: Not configured\n"
            
            # Data Sources
            message += f"\n📊 <b>Data Sources:</b>\n"
            events_detected = status.get('events_detected', 0)
            message += f"  🎯 Events Detected: {events_detected}\n"
            
            sources_active = status.get('sources_active', 0)
            message += f"  📡 Sources Active: {sources_active}\n"
            
            # Trading Status
            message += f"\n💼 <b>Trading:</b>\n"
            if status.get('paper_trading'):
                message += f"  📝 Mode: Paper Trading\n"
            else:
                message += f"  💰 Mode: Live Trading\n"
            
            positions = status.get('open_positions', 0)
            message += f"  📊 Open Positions: {positions}\n"
            
            # Performance
            if 'total_pnl' in status:
                pnl = status['total_pnl']
                pnl_emoji = "💚" if pnl > 0 else "❤️"
                message += f"  {pnl_emoji} Total P&L: ${pnl:+.2f}\n"
            
            message += f"\n⏰ <b>Updated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            message += f"\n🤖 <i>Project Chimera Status</i>"
            
            return await self.send_message(message)
            
        except Exception as e:
            logging.error(f"Failed to send system status: {e}")
            return False
    
    async def send_error_alert(self, error_type: str, error_message: str, component: str = "System") -> bool:
        """Send error alert to admin"""
        try:
            message = f"🚨 <b>ERROR ALERT</b> 🚨\n\n"
            message += f"⚠️ <b>Component:</b> {component}\n"
            message += f"🔴 <b>Error Type:</b> {error_type}\n"
            message += f"📝 <b>Message:</b> {error_message[:500]}...\n"  # Limit length
            message += f"\n⏰ <b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            message += f"\n🤖 <i>Project Chimera Error Monitor</i>"
            
            # Send to admin chat
            return await self.send_message(message, chat_id=self.admin_chat_id)
            
        except Exception as e:
            logging.error(f"Failed to send error alert: {e}")
            return False
    
    async def send_startup_message(self) -> bool:
        """Send system startup notification"""
        try:
            message = f"🚀 <b>PROJECT CHIMERA STARTED</b>\n\n"
            message += f"✅ System initialized successfully\n"
            message += f"📊 Monitoring unlock events\n"
            message += f"🔍 Data sources active\n"
            message += f"💼 Trading system ready\n"
            message += f"\n⏰ <b>Started:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            message += f"\n🤖 <i>Ready to detect unlock opportunities!</i>"
            
            return await self.send_message(message)
            
        except Exception as e:
            logging.error(f"Failed to send startup message: {e}")
            return False
    
    def send_sync(self, message: str, chat_id: Optional[str] = None) -> bool:
        """Synchronous wrapper for sending messages"""
        try:
            return asyncio.run(self.send_message(message, chat_id))
        except Exception as e:
            logging.error(f"Sync send failed: {e}")
            return False

# Global instance
telegram_notifier = ChimeraTelegramNotifier()

# Convenience functions
async def send_unlock_alert(unlock_event: Dict) -> bool:
    """Send unlock alert"""
    return await telegram_notifier.send_unlock_alert(unlock_event)

async def send_trading_signal(signal_type: str, token: str, details: Dict) -> bool:
    """Send trading signal"""
    return await telegram_notifier.send_trading_signal(signal_type, token, details)

async def send_system_status(status: Dict) -> bool:
    """Send system status"""
    return await telegram_notifier.send_system_status(status)

async def send_error_alert(error_type: str, error_message: str, component: str = "System") -> bool:
    """Send error alert"""
    return await telegram_notifier.send_error_alert(error_type, error_message, component)

if __name__ == "__main__":
    # Test the notification system
    async def test_notifications():
        print("🧪 Testing Telegram Notifications...")
        
        # Test startup message
        success = await telegram_notifier.send_startup_message()
        if success:
            print("✅ Startup message sent")
        else:
            print("❌ Failed to send startup message")
        
        # Test unlock alert
        test_unlock = {
            'token_symbol': 'UNI',
            'source': 'Test System',
            'confidence': 'high',
            'price_change_24h': -18.5,
            'unlock_amount': 100000000,
            'unlock_date': '2024-12-01T00:00:00Z'
        }
        
        success = await send_unlock_alert(test_unlock)
        if success:
            print("✅ Unlock alert sent")
        else:
            print("❌ Failed to send unlock alert")
        
        # Test trading signal
        success = await send_trading_signal('ENTRY', 'UNI', {
            'pressure_score': 0.8542,
            'entry_price': 8.45,
            'amount': 1000
        })
        if success:
            print("✅ Trading signal sent")
        else:
            print("❌ Failed to send trading signal")
        
        # Test system status
        success = await send_system_status({
            'infura_connected': True,
            'coingecko_active': True,
            'events_detected': 4,
            'sources_active': 3,
            'paper_trading': True,
            'open_positions': 0,
            'total_pnl': 0.0
        })
        if success:
            print("✅ System status sent")
        else:
            print("❌ Failed to send system status")
    
    # Run tests
    asyncio.run(test_notifications())
