#!/usr/bin/env python3
"""
2025 Integration Test Suite for Project Chimera
Tests WebSocket connections, Telegram notifications, and API integrations
"""

import os
import sys
import time
import json
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add service paths
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-oracle'))
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-herald'))

def test_environment_variables():
    """Test that all required environment variables are set"""
    print("🔧 Testing Environment Variables...")
    
    required_vars = {
        'TELEGRAM_BOT_TOKEN': 'Telegram bot token',
        'TELEGRAM_CHAT_ID': 'Telegram chat ID',
        'ETHERSCAN_API_KEY': 'Etherscan API key',
        'COINGECKO_API_KEY': 'CoinGecko API key',
        'INFURA_API_KEY': 'Infura API key'
    }
    
    missing_vars = []
    
    for var, description in required_vars.items():
        value = os.environ.get(var)
        if value:
            # Mask sensitive values
            if 'TOKEN' in var or 'KEY' in var:
                masked_value = value[:8] + '...' + value[-4:] if len(value) > 12 else '***'
                print(f"   ✅ {var}: {masked_value}")
            else:
                print(f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: Not set ({description})")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️ Missing variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def test_telegram_connection():
    """Test Telegram bot connection and send test message"""
    print("\n📱 Testing Telegram Connection...")
    
    try:
        from telegram_bot import send_telegram_message, test_telegram_connection
        
        # Test basic connection
        if test_telegram_connection():
            print("   ✅ Telegram bot connection successful")
            
            # Send test message
            test_message = """🧪 **2025 Test Message** 🧪
✅ Project Chimera is online
📡 All systems operational
⏰ Timestamp: """ + time.strftime("%Y-%m-%d %H:%M:%S UTC")
            
            if send_telegram_message(test_message):
                print("   ✅ Test message sent successfully")
                return True
            else:
                print("   ❌ Failed to send test message")
                return False
        else:
            print("   ❌ Telegram connection failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Telegram test error: {e}")
        return False

def test_binance_websocket_2025():
    """Test the enhanced 2025 Binance WebSocket implementation"""
    print("\n📡 Testing Binance WebSocket 2025...")
    
    try:
        from binance_realtime_2025 import BinanceWebSocket2025
        
        # Create monitor instance
        monitor = BinanceWebSocket2025()
        
        # Add test symbols
        test_symbols = ['BTC', 'ETH', 'UNI', 'AAVE']
        for symbol in test_symbols:
            monitor.add_symbol(symbol)
        
        print(f"   📊 Added {len(test_symbols)} symbols to monitor")
        
        # Test callback
        received_data = []
        def test_callback(event_type, symbol, data):
            received_data.append((event_type, symbol, data))
            if event_type == 'ticker':
                print(f"   📈 {symbol}: ${data['price']:.4f} ({data['price_change_24h']:+.2f}%)")
            elif event_type == 'connection':
                print(f"   🔗 Connection: {data['status']}")
        
        monitor.add_callback(test_callback)
        
        # Start monitoring
        if monitor.start_monitoring():
            print("   ✅ WebSocket connection initiated")
            
            # Wait for connection and data
            for i in range(30):  # Wait up to 30 seconds
                time.sleep(1)
                status = monitor.get_status()
                
                if status['connected'] and len(received_data) > 0:
                    print(f"   ✅ Received {len(received_data)} data points")
                    break
                elif i % 5 == 0:
                    print(f"   ⏳ Waiting for connection... ({i+1}/30)")
            
            # Check final status
            final_status = monitor.get_status()
            print(f"   📊 Final status: Connected={final_status['connected']}, Data points={len(received_data)}")
            
            # Stop monitoring
            monitor.stop_monitoring()
            
            return final_status['connected'] and len(received_data) > 0
        else:
            print("   ❌ Failed to start WebSocket monitoring")
            return False
            
    except Exception as e:
        print(f"   ❌ WebSocket test error: {e}")
        return False

def test_etherscan_api():
    """Test Etherscan API integration"""
    print("\n🔍 Testing Etherscan API...")
    
    try:
        import requests
        
        api_key = os.environ.get('ETHERSCAN_API_KEY')
        if not api_key:
            print("   ❌ ETHERSCAN_API_KEY not set")
            return False
        
        # Test V2 API with multiple chains
        chains = {
            1: "Ethereum",
            42161: "Arbitrum",
            8453: "Base"
        }
        
        for chain_id, chain_name in chains.items():
            try:
                url = "https://api.etherscan.io/v2/api"
                params = {
                    "chainid": chain_id,
                    "module": "account",
                    "action": "balance",
                    "address": "******************************************",
                    "tag": "latest",
                    "apikey": api_key
                }
                
                response = requests.get(url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if "result" in data:
                        balance = int(data["result"]) / 10**18
                        print(f"   ✅ {chain_name}: {balance:.6f} ETH")
                    else:
                        print(f"   ⚠️ {chain_name}: No result in response")
                else:
                    print(f"   ❌ {chain_name}: HTTP {response.status_code}")
                    
                time.sleep(0.2)  # Rate limiting
                
            except Exception as e:
                print(f"   ❌ {chain_name}: {e}")
        
        print("   ✅ Etherscan V2 API test completed")
        return True
        
    except Exception as e:
        print(f"   ❌ Etherscan test error: {e}")
        return False

def test_coingecko_api():
    """Test CoinGecko API integration"""
    print("\n🦎 Testing CoinGecko API...")
    
    try:
        import requests
        
        # Test basic API call
        url = "https://api.coingecko.com/api/v3/simple/price"
        params = {
            "ids": "bitcoin,ethereum,uniswap",
            "vs_currencies": "usd",
            "include_24hr_change": "true"
        }
        
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            for coin, info in data.items():
                price = info.get('usd', 0)
                change = info.get('usd_24h_change', 0)
                print(f"   📊 {coin.upper()}: ${price:,.2f} ({change:+.2f}%)")
            
            print("   ✅ CoinGecko API test successful")
            return True
        else:
            print(f"   ❌ CoinGecko API error: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ CoinGecko test error: {e}")
        return False

def test_paper_trading_mode():
    """Test paper trading functionality"""
    print("\n📋 Testing Paper Trading Mode...")
    
    try:
        # Set paper trading mode
        os.environ['PAPER_TRADING_MODE'] = 'true'
        
        # Import paper trading module
        sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-executioner'))
        from paper_trading import paper_engine, execute_paper_trade
        
        # Test candidate
        test_candidate = {
            'token_symbol': 'TEST',
            'contract_address': '0x1234567890123456789012345678901234567890',
            'unlock_date': '2025-02-01T00:00:00Z',
            'pressure_score': 1.5,
            'unlock_amount': 1000000,
            'circulating_supply': 10000000
        }
        
        # Get initial portfolio
        initial_portfolio = paper_engine.get_portfolio_summary()
        print(f"   📊 Initial portfolio: {initial_portfolio['total_value_usd']:.2f} USD")
        
        # Execute paper trade
        position = execute_paper_trade(test_candidate)
        print(f"   ✅ Paper trade executed: Position ID {position['position_id']}")
        
        # Get updated portfolio
        updated_portfolio = paper_engine.get_portfolio_summary()
        print(f"   📊 Updated portfolio: {updated_portfolio['total_value_usd']:.2f} USD")
        print(f"   📈 Open positions: {len(updated_portfolio['open_positions'])}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Paper trading test error: {e}")
        return False

def main():
    """Run all 2025 integration tests"""
    print("🚀 Project Chimera - 2025 Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Telegram Connection", test_telegram_connection),
        ("Binance WebSocket 2025", test_binance_websocket_2025),
        ("Etherscan API V2", test_etherscan_api),
        ("CoinGecko API", test_coingecko_api),
        ("Paper Trading Mode", test_paper_trading_mode),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ FAILED: {test_name} - {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 2025 INTEGRATION TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Project Chimera 2025 is ready for deployment.")
    elif passed > total // 2:
        print("⚠️ Most tests passed. Check failed tests and configuration.")
    else:
        print("❌ Multiple tests failed. Review configuration and API keys.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
