#!/usr/bin/env python3
"""
Test script to verify the updated Aave V3 integration works correctly.
This will test the borrowability checking for various tokens.
"""

import sys
import os
from pathlib import Path

# Add the seer service to the path
seer_path = Path(__file__).parent / 'services' / 'the-seer'
sys.path.insert(0, str(seer_path))

from onchain_checker import is_token_borrowable, is_borrowable_on_aave, is_major_token, get_lending_protocols_info

def test_token_borrowability():
    """Test borrowability checking for various tokens"""
    
    # Test tokens with their expected results
    test_tokens = [
        {
            "address": "0x1f9840a85d5af5bf1d1762f925bdaddc4201f984",
            "symbol": "UNI",
            "expected_major": True,
            "description": "Uniswap token - should be borrowable"
        },
        {
            "address": "0xc00e94cb662c3520282e6f5717214004a7f26888", 
            "symbol": "COMP",
            "expected_major": True,
            "description": "Compound token - should be borrowable"
        },
        {
            "address": "0x92d6c1e31e14519d225d5829cf70af773944c7f",
            "symbol": "DYDX",
            "expected_major": True,
            "description": "dYdX token - should be borrowable"
        },
        {
            "address": "0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9",
            "symbol": "AAVE",
            "expected_major": True,
            "description": "AAVE token - should be borrowable"
        },
        {
            "address": "0x1234567890123456789012345678901234567890",
            "symbol": "FAKE",
            "expected_major": False,
            "description": "Fake token - should not be borrowable"
        }
    ]
    
    print("🧪 Testing Aave V3 Integration")
    print("=" * 50)
    
    for token in test_tokens:
        print(f"\n📊 Testing {token['symbol']} ({token['description']})")
        print(f"   Address: {token['address']}")
        
        # Test major token check
        is_major = is_major_token(token['address'])
        major_status = "✅" if is_major == token['expected_major'] else "❌"
        print(f"   {major_status} Major token check: {is_major}")
        
        # Test Aave borrowability
        print(f"   🔍 Checking Aave V3 borrowability...")
        try:
            aave_borrowable = is_borrowable_on_aave(token['address'])
            print(f"   📋 Aave V3 result: {aave_borrowable}")
        except Exception as e:
            print(f"   ❌ Aave V3 error: {e}")
            aave_borrowable = False
        
        # Test overall borrowability
        try:
            overall_borrowable = is_token_borrowable(token['address'])
            print(f"   🎯 Overall borrowable: {overall_borrowable}")
        except Exception as e:
            print(f"   ❌ Overall check error: {e}")
        
        # Get detailed protocol info
        try:
            protocol_info = get_lending_protocols_info(token['address'])
            print(f"   📊 Protocol info: {protocol_info}")
        except Exception as e:
            print(f"   ❌ Protocol info error: {e}")
        
        print(f"   {'✅ PASS' if (is_major or aave_borrowable) else '❌ FAIL'}")

def test_pressure_score_calculation():
    """Test the pressure score calculation with updated data"""
    print("\n\n🧠 Testing Pressure Score Calculation")
    print("=" * 50)
    
    # Add analysis module to path
    from analysis import calculate_unlock_pressure_score, calculate_risk_metrics
    
    test_events = [
        {
            "token_symbol": "UNI",
            "contract_address": "0x1f9840a85d5af5bf1d1762f925bdaddc4201f984",
            "unlock_amount": 100000000,  # 100M tokens
            "circulating_supply": 750000000,  # 750M tokens
            "total_supply": 1000000000,  # 1B tokens
            "description": "Large UNI unlock event"
        },
        {
            "token_symbol": "COMP",
            "contract_address": "0xc00e94cb662c3520282e6f5717214004a7f26888",
            "unlock_amount": 50000000,  # 50M tokens
            "circulating_supply": 100000000,  # 100M tokens
            "total_supply": 10000000,  # 10M tokens (COMP has low supply)
            "description": "High-impact COMP unlock"
        }
    ]
    
    for event in test_events:
        print(f"\n📈 Testing {event['token_symbol']} - {event['description']}")
        
        try:
            # Calculate pressure score
            pressure_score = calculate_unlock_pressure_score(event)
            print(f"   🎯 Pressure Score: {pressure_score:.4f}")
            
            # Calculate risk metrics
            risk_metrics = calculate_risk_metrics(event)
            print(f"   ⚠️  Risk Metrics:")
            for key, value in risk_metrics.items():
                print(f"      {key}: {value}")
            
            # Check if it would be a trade candidate
            threshold = 0.75
            is_candidate = pressure_score > threshold
            print(f"   {'✅' if is_candidate else '❌'} Trade candidate (score > {threshold}): {is_candidate}")
            
        except Exception as e:
            print(f"   ❌ Error calculating metrics: {e}")

if __name__ == "__main__":
    print("🚀 Project Chimera - Aave Integration Test")
    print("Testing updated Aave V3 subgraph integration")
    print("=" * 60)
    
    try:
        test_token_borrowability()
        test_pressure_score_calculation()
        
        print("\n\n🎉 Test completed!")
        print("Check the results above to verify the integration is working.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
