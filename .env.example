# Project Chimera Environment Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/chimera_db
REDIS_URL=redis://localhost:6379

# Blockchain Configuration
INFURA_API_KEY=********************************
PRIVATE_KEY_PATH=/path/to/your/private/key/file

# Data Source APIs
TOKENUNLOCKS_API_KEY=your_tokenunlocks_api_key
VESTLAB_API_KEY=your_vestlab_api_key

# Telegram Notifications
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=6049830025
TELEGRAM_ADMIN_CHAT_ID=6049830025
TELEGRAM_API_ID=29395164
TELEGRAM_API_HASH=4fd72e3993e581776c5aabd3c88771cc

# Risk Management Parameters
PRESSURE_SCORE_THRESHOLD=0.75
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
TAKE_PROFIT_DAYS_BEFORE_UNLOCK=1
BORROW_AMOUNT_PER_TRADE=1000
MONITORING_INTERVAL_SECONDS=60

# Development Settings
PYTHONPATH=.
LOGGING_LEVEL=INFO
PAPER_TRADING_MODE=true
