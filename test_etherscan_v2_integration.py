#!/usr/bin/env python3
"""
Test script for Etherscan V2 API integration in Project Chimera
Demonstrates multi-chain unlock detection capabilities
"""

import os
import sys
import requests
import json
from pathlib import Path
from typing import Dict, List, Any

# Set up environment
os.environ['ETHERSCAN_API_KEY'] = '**********************************'

# Add Oracle service to path
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-oracle'))

def test_etherscan_v2_basic_connection():
    """Test basic connection to Etherscan V2 API"""
    print("🔗 Testing Etherscan V2 API Connection...")
    
    api_key = os.environ.get('ETHERSCAN_API_KEY')
    if not api_key:
        print("❌ ETHERSCAN_API_KEY not set")
        return False
    
    # Test connection across multiple chains
    chains = {
        1: "Ethereum Mainnet",
        42161: "Arbitrum One", 
        8453: "Base",
        10: "Optimism"
    }
    
    for chain_id, chain_name in chains.items():
        try:
            print(f"\n📡 Testing {chain_name} (Chain ID: {chain_id})...")
            
            # Test with a simple balance query
            url = "https://api.etherscan.io/v2/api"
            params = {
                "chainid": chain_id,
                "module": "account",
                "action": "balance",
                "address": "******************************************",  # Example address
                "tag": "latest",
                "apikey": api_key
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if "result" in data:
                    balance_wei = int(data["result"])
                    balance_eth = balance_wei / 10**18
                    print(f"   ✅ Connected successfully - Balance: {balance_eth:.6f} ETH")
                else:
                    print(f"   ⚠️ Connected but no result: {data}")
            else:
                print(f"   ❌ Connection failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error testing {chain_name}: {e}")
    
    return True

def test_multi_chain_token_analysis():
    """Test multi-chain token transfer analysis"""
    print("\n🔍 Testing Multi-Chain Token Transfer Analysis...")
    
    try:
        from data_sources import analyze_large_token_transfers
        
        api_key = os.environ.get('ETHERSCAN_API_KEY')
        indicators = analyze_large_token_transfers(api_key)
        
        print(f"📊 Found {len(indicators)} large transfer indicators")
        
        for indicator in indicators[:3]:  # Show first 3 results
            print(f"   🪙 {indicator.get('token_symbol', 'Unknown')} on Chain {indicator.get('chain_id', 'Unknown')}")
            print(f"      Amount: {indicator.get('transfer_amount', 0):,.0f} tokens")
            print(f"      Confidence: {indicator.get('confidence', 'unknown')}")
            print(f"      Source: {indicator.get('source', 'unknown')}")
        
        return len(indicators) > 0
        
    except Exception as e:
        print(f"❌ Multi-chain analysis failed: {e}")
        return False

def test_vesting_contract_monitoring():
    """Test vesting contract event monitoring"""
    print("\n🏗️ Testing Vesting Contract Monitoring...")
    
    try:
        from data_sources import analyze_vesting_contracts
        
        api_key = os.environ.get('ETHERSCAN_API_KEY')
        indicators = analyze_vesting_contracts(api_key)
        
        print(f"📊 Found {len(indicators)} vesting contract indicators")
        
        for indicator in indicators[:3]:  # Show first 3 results
            print(f"   📋 Contract: {indicator.get('contract_address', 'Unknown')}")
            print(f"      Chain: {indicator.get('chain_id', 'Unknown')}")
            print(f"      Confidence: {indicator.get('confidence', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vesting contract monitoring failed: {e}")
        return False

def test_gas_pattern_analysis():
    """Test gas usage pattern analysis"""
    print("\n⛽ Testing Gas Pattern Analysis...")
    
    try:
        from data_sources import analyze_gas_patterns
        
        api_key = os.environ.get('ETHERSCAN_API_KEY')
        
        # Test on Ethereum mainnet
        indicators = analyze_gas_patterns(api_key, 1)
        
        print(f"📊 Found {len(indicators)} gas pattern indicators")
        
        for indicator in indicators[:3]:  # Show first 3 results
            print(f"   ⛽ Block: {indicator.get('block_number', 'Unknown')}")
            print(f"      Gas Utilization: {indicator.get('gas_utilization', 0):.2%}")
            print(f"      Gas Used: {indicator.get('gas_used', 0):,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gas pattern analysis failed: {e}")
        return False

def test_complete_etherscan_integration():
    """Test the complete Etherscan integration"""
    print("\n🧪 Testing Complete Etherscan V2 Integration...")
    
    try:
        from data_sources import fetch_from_etherscan_api
        
        indicators = fetch_from_etherscan_api()
        
        print(f"📊 Complete integration found {len(indicators)} total indicators")
        
        # Group by source
        sources = {}
        for indicator in indicators:
            source = indicator.get('source', 'unknown')
            if source not in sources:
                sources[source] = 0
            sources[source] += 1
        
        print("\n📈 Indicators by source:")
        for source, count in sources.items():
            print(f"   {source}: {count}")
        
        return len(indicators) > 0
        
    except Exception as e:
        print(f"❌ Complete integration test failed: {e}")
        return False

def main():
    """Run all Etherscan V2 integration tests"""
    print("🚀 Project Chimera - Etherscan V2 API Integration Test")
    print("=" * 60)
    
    tests = [
        ("Basic Connection", test_etherscan_v2_basic_connection),
        ("Multi-Chain Token Analysis", test_multi_chain_token_analysis),
        ("Vesting Contract Monitoring", test_vesting_contract_monitoring),
        ("Gas Pattern Analysis", test_gas_pattern_analysis),
        ("Complete Integration", test_complete_etherscan_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "⚠️ NO DATA"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ FAILED: {test_name} - {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Etherscan V2 integration is working correctly.")
    elif passed > 0:
        print("⚠️ Some tests passed. Check configuration and API limits.")
    else:
        print("❌ All tests failed. Check API key and network connectivity.")

if __name__ == "__main__":
    main()
