#!/usr/bin/env python3
"""
Test The Executioner service wallet manager with real Infura connection
"""

import os
import sys
from pathlib import Path

# Add the executioner service to the path
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-executioner'))

# Set environment variable
os.environ['INFURA_API_KEY'] = '********************************'

def test_wallet_manager():
    """Test the wallet manager with real Infura connection"""
    print("🔗 Testing Executioner wallet manager...")
    
    try:
        from wallet_manager import get_eth_balance, get_token_balance, estimate_gas_price
        from web3 import Web3
        from eth_account import Account
        
        # Create a test account
        test_account = Account.create()
        print(f"✅ Test account created: {test_account.address}")
        
        # Test Web3 connection
        INFURA_API_KEY = os.environ.get("INFURA_API_KEY")
        WEB3_PROVIDER_URL = f"https://mainnet.infura.io/v3/{INFURA_API_KEY}"
        web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER_URL))
        
        if web3.is_connected():
            print("✅ Web3 connection successful")
            
            # Test ETH balance (should be 0 for new account)
            eth_balance = get_eth_balance(web3, test_account)
            print(f"💰 Test account ETH balance: {eth_balance} ETH")
            
            # Test gas price estimation
            gas_price = estimate_gas_price(web3)
            gas_price_gwei = web3.from_wei(gas_price, 'gwei')
            print(f"⛽ Current gas price: {gas_price_gwei:.2f} Gwei")
            
            # Test token balance (USDC)
            usdc_address = "******************************************"
            usdc_balance = get_token_balance(web3, test_account, usdc_address)
            print(f"🪙 Test account USDC balance: {usdc_balance} USDC")
            
            print("✅ All wallet manager functions working correctly!")
            return True
            
        else:
            print("❌ Web3 connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing wallet manager: {e}")
        return False

def test_lending_handler():
    """Test lending handler functions (without actual transactions)"""
    print("\n🏦 Testing lending handler...")
    
    try:
        from lending_handler import get_token_decimals, AAVE_V3_POOL_ADDRESS
        from web3 import Web3
        
        INFURA_API_KEY = os.environ.get("INFURA_API_KEY")
        WEB3_PROVIDER_URL = f"https://mainnet.infura.io/v3/{INFURA_API_KEY}"
        web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER_URL))
        
        if web3.is_connected():
            print("✅ Web3 connection for lending successful")
            
            # Test getting token decimals
            usdc_address = "******************************************"
            decimals = get_token_decimals(web3, usdc_address)
            print(f"🔢 USDC decimals: {decimals}")
            
            # Verify Aave pool address
            print(f"🏦 Aave V3 Pool address: {AAVE_V3_POOL_ADDRESS}")
            
            print("✅ Lending handler functions working correctly!")
            return True
            
        else:
            print("❌ Web3 connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing lending handler: {e}")
        return False

def test_dex_handler():
    """Test DEX handler functions (without actual transactions)"""
    print("\n🔄 Testing DEX handler...")
    
    try:
        from dex_handler import get_token_decimals
        from web3 import Web3
        
        INFURA_API_KEY = os.environ.get("INFURA_API_KEY")
        WEB3_PROVIDER_URL = f"https://mainnet.infura.io/v3/{INFURA_API_KEY}"
        web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER_URL))
        
        if web3.is_connected():
            print("✅ Web3 connection for DEX successful")
            
            # Test getting token decimals for different tokens
            tokens = {
                "USDC": "******************************************",
                "USDT": "******************************************",
                "WETH": "******************************************"
            }
            
            for symbol, address in tokens.items():
                decimals = get_token_decimals(web3, address)
                print(f"🔢 {symbol} decimals: {decimals}")
            
            print("✅ DEX handler functions working correctly!")
            return True
            
        else:
            print("❌ Web3 connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing DEX handler: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Project Chimera - Executioner Service Test")
    print("=" * 50)
    
    success = True
    
    # Test wallet manager
    success &= test_wallet_manager()
    
    # Test lending handler
    success &= test_lending_handler()
    
    # Test DEX handler
    success &= test_dex_handler()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All Executioner service tests passed!")
        print("\n📝 The Executioner service is ready for:")
        print("✅ Wallet management and balance checking")
        print("✅ Aave V3 lending protocol integration")
        print("✅ DEX token swapping preparation")
        print("\n⚠️  To execute actual trades, you'll need:")
        print("1. A funded wallet with ETH for gas")
        print("2. Collateral deposited in Aave for borrowing")
        print("3. Proper risk management settings")
    else:
        print("❌ Some Executioner service tests failed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
