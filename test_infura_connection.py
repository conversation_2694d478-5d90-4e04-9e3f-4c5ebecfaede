#!/usr/bin/env python3
"""
Test script to verify Infura connection with the provided API key
"""

import os
import sys
from web3 import Web3

# Load environment variables
INFURA_API_KEY = "********************************"
WEB3_PROVIDER_URL = f"https://mainnet.infura.io/v3/{INFURA_API_KEY}"

def test_infura_connection():
    """Test connection to Ethereum mainnet via Infura"""
    print("🔗 Testing Infura connection...")
    print(f"API Key: {INFURA_API_KEY}")
    print(f"Provider URL: {WEB3_PROVIDER_URL}")
    
    try:
        # Create Web3 instance
        web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER_URL))
        
        # Test connection
        if web3.is_connected():
            print("✅ Successfully connected to Ethereum mainnet!")
            
            # Get current block number
            block_number = web3.eth.block_number
            print(f"📊 Current block number: {block_number:,}")
            
            # Get latest block info
            latest_block = web3.eth.get_block('latest')
            print(f"⏰ Latest block timestamp: {latest_block.timestamp}")
            print(f"🔢 Latest block hash: {latest_block.hash.hex()}")
            
            # Test gas price
            gas_price = web3.eth.gas_price
            gas_price_gwei = web3.from_wei(gas_price, 'gwei')
            print(f"⛽ Current gas price: {gas_price_gwei:.2f} Gwei")
            
            # Test a simple contract call (ETH balance of a known address)
            vitalik_address = "******************************************"  # Vitalik's address
            balance = web3.eth.get_balance(vitalik_address)
            balance_eth = web3.from_wei(balance, 'ether')
            print(f"💰 Vitalik's ETH balance: {balance_eth:.4f} ETH")
            
            return True
            
        else:
            print("❌ Failed to connect to Ethereum mainnet")
            return False
            
    except Exception as e:
        print(f"❌ Error connecting to Infura: {e}")
        return False

def test_wallet_functionality():
    """Test wallet-related functionality (without actual private key)"""
    print("\n🔐 Testing wallet functionality...")
    
    try:
        from eth_account import Account
        
        # Generate a test account (for demonstration only)
        test_account = Account.create()
        print(f"✅ Test account created: {test_account.address}")
        print(f"🔑 Private key format: 0x{'*' * 60}... (hidden for security)")
        
        # Test signing a message
        from eth_account.messages import encode_defunct
        message = "Test message for Project Chimera"
        message_hash = encode_defunct(text=message)
        signed_message = test_account.sign_message(message_hash)
        print(f"✅ Message signing test successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing wallet functionality: {e}")
        return False

def test_token_contract_interaction():
    """Test interaction with a known ERC20 token contract"""
    print("\n🪙 Testing token contract interaction...")
    
    try:
        web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER_URL))
        
        if not web3.is_connected():
            print("❌ Not connected to Ethereum")
            return False
        
        # USDC contract address
        usdc_address = "******************************************"
        
        # Basic ERC20 ABI for testing
        erc20_abi = [
            {
                "constant": True,
                "inputs": [],
                "name": "name",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "symbol",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "totalSupply",
                "outputs": [{"name": "", "type": "uint256"}],
                "type": "function"
            }
        ]
        
        # Create contract instance
        usdc_contract = web3.eth.contract(address=usdc_address, abi=erc20_abi)
        
        # Test contract calls
        name = usdc_contract.functions.name().call()
        symbol = usdc_contract.functions.symbol().call()
        decimals = usdc_contract.functions.decimals().call()
        total_supply = usdc_contract.functions.totalSupply().call()
        
        print(f"✅ Token contract interaction successful:")
        print(f"   Name: {name}")
        print(f"   Symbol: {symbol}")
        print(f"   Decimals: {decimals}")
        print(f"   Total Supply: {total_supply:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing token contract interaction: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Project Chimera - Infura Connection Test")
    print("=" * 50)
    
    success = True
    
    # Test Infura connection
    success &= test_infura_connection()
    
    # Test wallet functionality
    success &= test_wallet_functionality()
    
    # Test token contract interaction
    success &= test_token_contract_interaction()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Your Infura connection is working correctly.")
        print("\n📝 Next steps:")
        print("1. Set up a test wallet with some ETH for gas")
        print("2. Configure other API keys (TokenUnlocks, Vestlab, Telegram)")
        print("3. Set up local PostgreSQL and Redis for testing")
        print("4. Run the full system tests")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
