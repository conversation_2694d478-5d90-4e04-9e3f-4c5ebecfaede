#!/usr/bin/env python3
"""
Telegram Configuration Diagnostic and Fix Script
Identifies and resolves TELEGRAM_CHAT_ID loading issues
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def diagnose_env_loading():
    """Diagnose environment variable loading issues"""
    print("🔍 Diagnosing Environment Variable Loading...")
    
    # Check if .env file exists
    env_file = Path('.env')
    if env_file.exists():
        print(f"   ✅ .env file found: {env_file.absolute()}")
        print(f"   📏 File size: {env_file.stat().st_size} bytes")
    else:
        print("   ❌ .env file not found!")
        return False
    
    # Read .env file directly
    print("\n📄 Reading .env file directly...")
    try:
        with open('.env', 'r') as f:
            lines = f.readlines()
        
        telegram_lines = [line.strip() for line in lines if 'TELEGRAM_CHAT_ID' in line and not line.strip().startswith('#')]
        
        if telegram_lines:
            for line in telegram_lines:
                print(f"   📝 Found: {line}")
        else:
            print("   ❌ TELEGRAM_CHAT_ID not found in .env file")
            return False
            
    except Exception as e:
        print(f"   ❌ Error reading .env file: {e}")
        return False
    
    # Test dotenv loading
    print("\n🔄 Testing dotenv loading...")
    
    # Clear existing environment variable
    if 'TELEGRAM_CHAT_ID' in os.environ:
        print(f"   ℹ️ TELEGRAM_CHAT_ID already in environment: {os.environ['TELEGRAM_CHAT_ID']}")
        del os.environ['TELEGRAM_CHAT_ID']
    
    # Load .env file
    load_result = load_dotenv()
    print(f"   📥 load_dotenv() result: {load_result}")
    
    # Check if loaded
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    if chat_id:
        print(f"   ✅ TELEGRAM_CHAT_ID loaded: {chat_id}")
        return True
    else:
        print("   ❌ TELEGRAM_CHAT_ID still not loaded")
        return False

def test_different_loading_methods():
    """Test different methods of loading environment variables"""
    print("\n🧪 Testing Different Loading Methods...")
    
    methods = [
        ("Direct os.environ", lambda: os.environ.get('TELEGRAM_CHAT_ID')),
        ("load_dotenv() then os.environ", lambda: load_dotenv() or os.environ.get('TELEGRAM_CHAT_ID')),
        ("load_dotenv(override=True)", lambda: load_dotenv(override=True) or os.environ.get('TELEGRAM_CHAT_ID')),
        ("load_dotenv('.env')", lambda: load_dotenv('.env') or os.environ.get('TELEGRAM_CHAT_ID')),
    ]
    
    for method_name, method_func in methods:
        try:
            # Clear environment variable first
            if 'TELEGRAM_CHAT_ID' in os.environ:
                del os.environ['TELEGRAM_CHAT_ID']
            
            result = method_func()
            status = "✅" if result else "❌"
            print(f"   {status} {method_name}: {result}")
            
        except Exception as e:
            print(f"   ❌ {method_name}: Error - {e}")

def manual_env_parsing():
    """Manually parse .env file to extract TELEGRAM_CHAT_ID"""
    print("\n🔧 Manual .env Parsing...")
    
    try:
        with open('.env', 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line.startswith('TELEGRAM_CHAT_ID=') and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    print(f"   📍 Line {line_num}: {key}={value}")
                    
                    # Set manually
                    os.environ[key] = value
                    print(f"   ✅ Manually set {key}={value}")
                    
                    # Verify
                    check_value = os.environ.get('TELEGRAM_CHAT_ID')
                    print(f"   🔍 Verification: {check_value}")
                    
                    return value
        
        print("   ❌ TELEGRAM_CHAT_ID not found in manual parsing")
        return None
        
    except Exception as e:
        print(f"   ❌ Manual parsing error: {e}")
        return None

def test_telegram_connection():
    """Test Telegram connection with the loaded chat ID"""
    print("\n📱 Testing Telegram Connection...")
    
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
    
    if not chat_id:
        print("   ❌ TELEGRAM_CHAT_ID not available")
        return False
    
    if not bot_token:
        print("   ❌ TELEGRAM_BOT_TOKEN not available")
        return False
    
    print(f"   📊 Chat ID: {chat_id}")
    print(f"   🤖 Bot Token: {bot_token[:10]}...{bot_token[-10:]}")
    
    try:
        import requests
        
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            "chat_id": chat_id,
            "text": "🔧 **Configuration Test** 🔧\n✅ TELEGRAM_CHAT_ID is now working correctly!",
            "parse_mode": "Markdown"
        }
        
        response = requests.post(url, json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("ok"):
                print("   ✅ Test message sent successfully!")
                return True
            else:
                print(f"   ❌ Telegram API error: {result.get('description')}")
                return False
        else:
            print(f"   ❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Connection test error: {e}")
        return False

def create_fixed_env_loader():
    """Create a robust environment loader function"""
    print("\n🛠️ Creating Fixed Environment Loader...")
    
    loader_code = '''
def load_env_robust():
    """Robust environment variable loader for Project Chimera"""
    import os
    from pathlib import Path
    from dotenv import load_dotenv
    
    # Try multiple loading strategies
    strategies = [
        lambda: load_dotenv(override=True),
        lambda: load_dotenv('.env', override=True),
        lambda: load_dotenv(Path('.env'), override=True),
    ]
    
    for i, strategy in enumerate(strategies, 1):
        try:
            result = strategy()
            if result and os.environ.get('TELEGRAM_CHAT_ID'):
                print(f"✅ Environment loaded with strategy {i}")
                return True
        except Exception as e:
            print(f"❌ Strategy {i} failed: {e}")
            continue
    
    # Manual fallback
    try:
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            
            if os.environ.get('TELEGRAM_CHAT_ID'):
                print("✅ Environment loaded with manual fallback")
                return True
    except Exception as e:
        print(f"❌ Manual fallback failed: {e}")
    
    return False

# Usage in your scripts:
# from env_loader import load_env_robust
# load_env_robust()
'''
    
    with open('env_loader.py', 'w') as f:
        f.write(loader_code)
    
    print("   ✅ Created env_loader.py with robust loading function")
    return True

def main():
    """Main diagnostic and fix routine"""
    print("🔧 Telegram Configuration Diagnostic & Fix")
    print("=" * 50)
    
    # Step 1: Diagnose environment loading
    if not diagnose_env_loading():
        print("\n❌ Basic environment loading failed")
    
    # Step 2: Test different loading methods
    test_different_loading_methods()
    
    # Step 3: Manual parsing as fallback
    manual_result = manual_env_parsing()
    
    # Step 4: Test Telegram connection
    if manual_result:
        test_telegram_connection()
    
    # Step 5: Create robust loader
    create_fixed_env_loader()
    
    # Final verification
    print("\n🎯 Final Verification...")
    final_chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    if final_chat_id:
        print(f"✅ TELEGRAM_CHAT_ID is now available: {final_chat_id}")
        print("\n🚀 Quick Fix for your scripts:")
        print("Add this at the top of your Python files:")
        print("```python")
        print("import os")
        print("from dotenv import load_dotenv")
        print("load_dotenv(override=True)  # Force reload")
        print("```")
        return True
    else:
        print("❌ TELEGRAM_CHAT_ID still not available")
        print("\n🔧 Manual Fix:")
        print("Add this line to your Python scripts:")
        print("```python")
        print("os.environ['TELEGRAM_CHAT_ID'] = '6049830025'")
        print("```")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 Configuration fixed! Your Telegram integration should now work.")
    else:
        print("\n⚠️ Manual intervention required. Check the suggestions above.")
