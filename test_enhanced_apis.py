#!/usr/bin/env python3
"""
Test Enhanced API Integration
Tests CoinGecko API with real key and Binance WebSocket integration
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add services to path
sys.path.append('services/the-oracle')

def test_coingecko_api():
    """Test CoinGecko API with real key"""
    print("🔍 Testing CoinGecko API...")
    
    try:
        from data_sources import fetch_from_coingecko_api
        
        events = fetch_from_coingecko_api()
        print(f"✅ CoinGecko: {len(events)} unlock indicators found")
        
        if events:
            print("📊 Sample CoinGecko data:")
            for event in events[:3]:
                symbol = event.get('token_symbol', 'UNKNOWN')
                change = event.get('price_change_24h', 0)
                print(f"  - {symbol}: {change:+.2f}% (24h)")
        
        return True
        
    except Exception as e:
        print(f"❌ CoinGecko test failed: {e}")
        return False

def test_defillama_api():
    """Test DeFiLlama API integration"""
    print("\n🔍 Testing DeFiLlama API...")
    
    try:
        from data_sources import fetch_from_defillama_api
        
        events = fetch_from_defillama_api()
        print(f"✅ DeFiLlama: {len(events)} unlock indicators found")
        
        if events:
            print("📊 Sample DeFiLlama data:")
            for event in events[:3]:
                protocol = event.get('protocol_name', 'UNKNOWN')
                tvl_change = event.get('tvl_change_1d', 0)
                print(f"  - {protocol}: {tvl_change:+.2f}% TVL change")
        
        return True
        
    except Exception as e:
        print(f"❌ DeFiLlama test failed: {e}")
        return False

def test_binance_websocket():
    """Test Binance WebSocket integration"""
    print("\n🔍 Testing Binance WebSocket...")
    
    try:
        from binance_websocket import BinanceWebSocketManager
        
        # Create WebSocket manager
        ws_manager = BinanceWebSocketManager()
        
        # Test callback function
        def test_callback(event_type, symbol, data):
            if event_type == 'unlock_indicator':
                print(f"🚨 UNLOCK ALERT: {symbol}")
                print(f"   Indicators: {data['indicators']}")
            elif event_type == 'ticker':
                price = data['c']
                change = data['P']
                print(f"📊 {symbol}: ${price} ({change:+.2f}%)")
        
        # Add callback and start monitoring
        ws_manager.add_callback(test_callback)
        
        # Test with a few DeFi tokens
        test_symbols = ['UNI', 'AAVE', 'COMP']
        print(f"🚀 Starting WebSocket monitoring for: {', '.join(test_symbols)}")
        
        ws_manager.start_monitoring(test_symbols)
        
        # Wait a bit for connection
        print("⏳ Waiting for WebSocket connection...")
        for i in range(10):
            if ws_manager.is_connected:
                print("✅ Binance WebSocket connected successfully!")
                break
            time.sleep(1)
        else:
            print("⚠️ WebSocket connection timeout (this is normal for testing)")
        
        # Stop monitoring
        ws_manager.stop_monitoring()
        return True
        
    except Exception as e:
        print(f"❌ Binance WebSocket test failed: {e}")
        return False

def test_full_integration():
    """Test full data source integration"""
    print("\n🔍 Testing Full Integration...")
    
    try:
        from data_sources import fetch_token_unlocks_data
        
        print("🚀 Fetching data from all sources...")
        events = fetch_token_unlocks_data()
        
        print(f"✅ Total events detected: {len(events)}")
        
        # Group by source
        sources = {}
        for event in events:
            source = event.get('source', 'Unknown')
            if source not in sources:
                sources[source] = 0
            sources[source] += 1
        
        print("\n📊 Events by source:")
        for source, count in sources.items():
            print(f"  - {source}: {count} events")
        
        # Show sample events
        if events:
            print("\n🎯 Sample unlock events:")
            for i, event in enumerate(events[:5]):
                symbol = event.get('token_symbol', 'UNKNOWN')
                source = event.get('source', 'Unknown')
                confidence = event.get('confidence', 'unknown')
                print(f"  {i+1}. {symbol} ({source}) - {confidence} confidence")
        
        return True
        
    except Exception as e:
        print(f"❌ Full integration test failed: {e}")
        return False

def test_api_keys():
    """Test API key configuration"""
    print("\n🔑 Testing API Key Configuration...")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    api_keys = {
        'COINGECKO_API_KEY': os.environ.get('COINGECKO_API_KEY'),
        'DEXSCREENER_API_KEY': os.environ.get('DEXSCREENER_API_KEY'),
        'DEXTOOLS_API_KEY': os.environ.get('DEXTOOLS_API_KEY'),
        'DEFILLAMA_PRO_API_KEY': os.environ.get('DEFILLAMA_PRO_API_KEY'),
        'THEGRAPH_API_KEY': os.environ.get('THEGRAPH_API_KEY'),
    }
    
    for key_name, key_value in api_keys.items():
        if key_value and key_value != f"your_{key_name.lower()}_here":
            if key_name == 'COINGECKO_API_KEY':
                print(f"✅ {key_name}: {key_value[:10]}... (ACTIVE)")
            else:
                print(f"🔑 {key_name}: Set (not active)")
        else:
            print(f"⭕ {key_name}: Not set")
    
    return True

def main():
    """Run all tests"""
    print("🧪 Project Chimera - Enhanced API Integration Tests")
    print("=" * 60)
    
    # Configure logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    
    tests = [
        ("API Keys", test_api_keys),
        ("CoinGecko API", test_coingecko_api),
        ("DeFiLlama API", test_defillama_api),
        ("Binance WebSocket", test_binance_websocket),
        ("Full Integration", test_full_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Enhanced API integration is working perfectly.")
    elif passed >= len(results) * 0.8:
        print("✅ Most tests passed! System is functional with minor issues.")
    else:
        print("⚠️ Some tests failed. Check configuration and network connectivity.")
    
    print("\n💡 Next steps:")
    print("  1. Get additional API keys for enhanced features")
    print("  2. Test with real trading scenarios")
    print("  3. Monitor unlock detection accuracy")
    print("  4. Consider upgrading to premium tiers")

if __name__ == "__main__":
    main()
