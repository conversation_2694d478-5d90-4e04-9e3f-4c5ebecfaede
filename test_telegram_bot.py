#!/usr/bin/env python3
"""
Test script to verify Telegram bot functionality for Project Chimera
"""

import os
import sys
from pathlib import Path

# Add the herald service to the path
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-herald'))

# Set environment variables
os.environ['TELEGRAM_BOT_TOKEN'] = '**********************************************'
os.environ['TELEGRAM_CHAT_ID'] = '6049830025'

def test_telegram_connection():
    """Test basic Telegram bot connection"""
    print("🤖 Testing Telegram bot connection...")
    
    try:
        from telegram_bot import get_bot_info, test_telegram_connection
        
        # Get bot information
        bot_info = get_bot_info()
        if bot_info:
            print(f"✅ Bot connected successfully!")
            print(f"   Bot name: {bot_info.get('first_name', 'Unknown')}")
            print(f"   Bot username: @{bot_info.get('username', 'Unknown')}")
            print(f"   Bot ID: {bot_info.get('id', 'Unknown')}")
            
            # Test sending a message
            success = test_telegram_connection()
            if success:
                print("✅ Test message sent successfully!")
                return True
            else:
                print("❌ Failed to send test message")
                return False
        else:
            print("❌ Failed to get bot information")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Telegram connection: {e}")
        return False

def test_herald_notifications():
    """Test The Herald notification formatting"""
    print("\n📢 Testing Herald notification system...")
    
    try:
        from main import format_message
        from telegram_bot import send_telegram_message
        
        # Test different message types
        test_messages = [
            {
                "channel": "chimera:trade_candidates",
                "data": {
                    "token_symbol": "DYDX",
                    "pressure_score": 2.45,
                    "unlock_date": "2024-12-01T00:00:00Z",
                    "unlock_amount": 150000000,
                    "circulating_supply": 300000000
                }
            },
            {
                "channel": "chimera:position_opened", 
                "data": {
                    "position_id": 1,
                    "token_symbol": "UNI",
                    "amount_shorted": "1000",
                    "entry_price_in_usdc": "8.50"
                }
            },
            {
                "channel": "chimera:close_position",
                "data": {
                    "position_id": 1,
                    "token_symbol": "AAVE",
                    "reason": "Take-Profit triggered at $95.50 (Target: $90.00, Profit: 12.50%)",
                    "current_price": "95.50",
                    "entry_price": "85.00"
                }
            }
        ]
        
        print("📝 Formatting test messages...")
        for i, test_msg in enumerate(test_messages, 1):
            try:
                formatted = format_message(test_msg["channel"], test_msg["data"])
                print(f"✅ Message {i} formatted successfully")
                print(f"   Preview: {formatted[:50]}...")
            except Exception as e:
                print(f"❌ Message {i} formatting failed: {e}")
                return False
        
        # Send a comprehensive test notification
        test_notification = """🚀 **Project Chimera System Test** 🚀

🔗 **Blockchain Connection**: ✅ Connected to Ethereum Mainnet
⛽ **Gas Price**: ~0.55 Gwei (Very Low!)
🏦 **Aave V3**: Ready for lending operations
🔄 **DEX Integration**: 1inch + Uniswap ready
📊 **Risk Management**: All parameters configured

🎯 **System Status**: FULLY OPERATIONAL
📈 **Ready for**: Automated token unlock arbitrage

⚠️ **Next Steps**:
• Fund trading wallet with ETH
• Deploy to production environment
• Monitor first trades closely

🤖 **Notification System**: Working perfectly!"""

        print("\n📤 Sending comprehensive test notification...")
        success = send_telegram_message(test_notification)
        
        if success:
            print("✅ Comprehensive test notification sent!")
            return True
        else:
            print("❌ Failed to send comprehensive notification")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Herald notifications: {e}")
        return False

def test_system_startup_notification():
    """Test system startup notification"""
    print("\n🚀 Testing system startup notification...")
    
    try:
        from main import send_startup_notification
        
        send_startup_notification()
        print("✅ Startup notification sent!")
        return True
        
    except Exception as e:
        print(f"❌ Error sending startup notification: {e}")
        return False

def test_error_notifications():
    """Test error notification handling"""
    print("\n🚨 Testing error notification handling...")
    
    try:
        from telegram_bot import send_telegram_message
        
        # Test error message
        error_message = """🚨 **System Alert** 🚨

⚠️ **Alert Type**: Test Error Notification
🔧 **Component**: Notification System Test
📊 **Status**: Testing error handling capabilities

💡 **Details**: This is a test of the error notification system. In a real scenario, this would contain specific error details and recommended actions.

✅ **Action Required**: None (this is just a test)
🕐 **Timestamp**: System test in progress"""

        success = send_telegram_message(error_message)
        
        if success:
            print("✅ Error notification test successful!")
            return True
        else:
            print("❌ Error notification test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing error notifications: {e}")
        return False

def main():
    """Main test function"""
    print("🤖 Project Chimera - Telegram Bot Test")
    print("=" * 50)
    
    success = True
    
    # Test basic connection
    success &= test_telegram_connection()
    
    # Test Herald notifications
    success &= test_herald_notifications()
    
    # Test startup notification
    success &= test_system_startup_notification()
    
    # Test error notifications
    success &= test_error_notifications()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All Telegram tests passed!")
        print("\n📱 Check your Telegram chat for test messages!")
        print("\n✅ **Telegram Integration Status**: FULLY OPERATIONAL")
        print("\n📋 **Notification Types Tested**:")
        print("   • Trade candidate alerts")
        print("   • Position opened/closed notifications") 
        print("   • System startup messages")
        print("   • Error and warning alerts")
        print("\n🚀 **Ready for Production**: Telegram notifications are working perfectly!")
    else:
        print("❌ Some Telegram tests failed. Please check the errors above.")
        print("\n🔧 **Troubleshooting Tips**:")
        print("   • Verify bot token is correct")
        print("   • Ensure chat ID is accurate")
        print("   • Check if bot is added to the chat")
        print("   • Verify internet connection")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
